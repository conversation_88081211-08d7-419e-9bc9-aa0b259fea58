"""
PPT设计基因和视觉指导相关提示词
包含所有用于设计分析和视觉指导的提示词模板
"""

from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)


def _is_image_service_enabled() -> bool:
    """检查图片服务是否启用和可用"""
    try:
        # 尝试获取图片服务实例
        from ..service_instances import get_ppt_service
        ppt_service = get_ppt_service()

        # 检查图片服务是否存在且已初始化
        if not ppt_service.image_service:
            return False

        # 检查图片服务是否已初始化
        if not ppt_service.image_service.initialized:
            return False

        # 检查是否有可用的提供者
        from ..image.providers.base import provider_registry

        # 检查是否有AI生成提供者
        generation_providers = provider_registry.get_generation_providers(enabled_only=True)

        # 检查是否有网络搜索提供者
        search_providers = provider_registry.get_search_providers(enabled_only=True)

        # 检查是否有本地存储提供者（总是可用）
        storage_providers = provider_registry.get_storage_providers(enabled_only=True)

        # 如果有任何可用的提供者（AI生成、网络搜索或本地存储），则认为服务可用
        has_providers = len(generation_providers) > 0 or len(search_providers) > 0 or len(storage_providers) > 0

        logger.debug(f"Image service status: initialized={ppt_service.image_service.initialized}, "
                    f"generation_providers={len(generation_providers)}, "
                    f"search_providers={len(search_providers)}, "
                    f"storage_providers={len(storage_providers)}")

        return has_providers

    except Exception as e:
        logger.debug(f"Failed to check image service status: {e}")
        return False


class DesignPrompts:
    """PPT设计基因和视觉指导相关的提示词集合"""
    
    @staticmethod
    def get_style_gene_extraction_prompt(template_code: str) -> str:
        """获取设计基因提取提示词"""
        return f"""作为专业的UI/UX设计师，请分析以下HTML模板代码，提取其核心设计基因。

**模板代码：**
```html
{template_code}
```

请从以下维度分析并提取设计基因：

1. **色彩系统**：主色调、辅助色、背景色、文字色等
2. **字体系统**：字体族、字号层级、字重搭配等
3. **布局结构**：页面布局、间距规律、对齐方式等
4. **视觉元素**：边框样式、阴影效果、圆角设计等
5. **交互效果**：动画效果、悬停状态、过渡效果等
6. **组件风格**：按钮样式、卡片设计、图标风格等

请以结构化的方式输出设计基因，包含具体的CSS属性值和设计规律，以便后续页面能够保持一致的视觉风格。

输出格式：
- 每个维度用明确的标题分隔
- 提供具体的CSS属性和数值
- 说明设计规律和应用场景
- 突出关键的视觉特征"""

    @staticmethod
    def get_unified_design_guide_prompt(slide_data: Dict[str, Any], page_number: int, total_pages: int) -> str:
        """获取统一设计指导提示词"""

        # 处理图片信息 - 只有在图片服务启用且有图片信息时才包含
        images_context = ""
        if _is_image_service_enabled() and 'images_summary' in slide_data:
            images_context = f"""


**图片设计指导要求：**
- 请充分考虑这些图片资源在设计中的运用
- 根据图片的用途、内容描述和尺寸信息提供具体的布局建议
- 考虑图片的实际尺寸（宽度x高度）来优化布局和比例
- 根据图片文件大小和格式选择合适的显示方式
- 确保图片与页面内容的协调性和视觉平衡
- 提供图片样式和位置的优化建议
"""

        return f"""作为资深的PPT设计师，请为以下幻灯片生成全面的创意设计指导，包含创意变化指导和内容驱动的设计建议：

**完整幻灯片数据：**
{slide_data}

**页面位置：**第{page_number}页（共{total_pages}页）

{images_context}

请从以下角度生成统一的设计指导：

**A. 页面定位与创意策略**：
- 分析该页面在整体PPT中的作用和重要性
- 确定页面的核心信息传达目标
- 提出符合页面定位的创意设计方向

**B. 视觉层级与布局建议**：
- 根据内容重要性设计视觉层级
- 提供具体的布局方案和元素排列建议
- 考虑信息密度和视觉平衡

**C. 色彩与风格应用**：
- 基于内容特点选择合适的色彩方案
- 提供具体的色彩搭配建议
- 确保与整体PPT风格的一致性

**D. 交互与动效建议**：
- 根据页面类型提供合适的交互效果
- 建议页面切换和元素动画
- 增强用户体验和视觉吸引力

**E. 内容优化建议**：
- 分析内容要点的表达方式
- 提供信息可视化建议
- 优化文字表达和信息结构

请提供具体、可操作的设计指导，帮助生成高质量的PPT页面。"""

    @staticmethod
    def get_creative_variation_prompt(slide_data: Dict[str, Any], page_number: int, total_pages: int) -> str:
        """获取创意变化指导提示词"""
        return f"""作为创意设计专家，请为以下幻灯片提供创意变化指导：

**幻灯片数据：**
{slide_data}

**页面位置：**第{page_number}页（共{total_pages}页）

请从以下角度提供创意指导：

**1. 视觉创意方向**：
- 根据页面内容特点，提出独特的视觉表现方式
- 建议创新的布局形式和元素组合
- 提供差异化的设计思路

**2. 交互创意建议**：
- 设计有趣的页面交互效果
- 提供动态元素的创意应用
- 增强用户参与感和体验感

**3. 内容呈现创新**：
- 优化信息的可视化表达
- 提供创意的内容组织方式
- 增强信息的传达效果

**4. 风格变化控制**：
- 在保持整体一致性的前提下，提供适度的风格变化
- 确保创意不影响信息传达的清晰度
- 平衡创新性与实用性

请提供具体的创意实施建议。"""

    @staticmethod
    def get_content_driven_design_prompt(slide_data: Dict[str, Any], page_number: int, total_pages: int) -> str:
        """获取内容驱动设计建议提示词"""
        return f"""作为内容驱动设计专家，请为以下幻灯片提供基于内容的设计建议：

**幻灯片数据：**
{slide_data}

**页面位置：**第{page_number}页（共{total_pages}页）

请从以下角度提供设计建议：

**1. 内容分析与层级**：
- 分析页面内容的重要性层级
- 确定主要信息和次要信息
- 提供信息优先级排序建议

**2. 视觉表达策略**：
- 根据内容类型选择最佳的视觉表达方式
- 提供图表、图像、文字的组合建议
- 优化信息的可读性和理解性

**3. 布局优化方案**：
- 基于内容特点设计最佳布局
- 确保信息流的逻辑性和连贯性
- 提供空间利用的优化建议

**4. 用户体验考虑**：
- 从目标受众角度优化设计
- 确保信息传达的有效性
- 提高用户的理解和记忆效果

请提供具体的设计实施方案。"""

    @staticmethod
    def get_style_genes_extraction_prompt(template_html: str) -> str:
        """获取设计基因提取提示词"""
        return f"""请分析以下HTML模板，提取其核心设计基因：

**HTML模板：**
```html
{template_html}
```

请从以下维度提取设计基因：

**1. 色彩基因**：
- 主色调和辅助色彩
- 色彩搭配规律
- 色彩的情感表达

**2. 字体基因**：
- 字体族和字号规律
- 字重和字间距特点
- 文字层次结构

**3. 布局基因**：
- 页面结构和比例
- 元素间距和对齐方式
- 空间利用特点

**4. 视觉基因**：
- 装饰元素和图形风格
- 边框和阴影效果
- 视觉重点突出方式

**5. 交互基因**：
- 动效和过渡效果
- 用户体验设计特点
- 响应式设计规律

请提供详细的设计基因分析报告，确保后续页面能够保持一致的设计风格。"""

    @staticmethod
    def get_creative_template_context_prompt(slide_data: Dict[str, Any], template_html: str,
                                           slide_title: str, slide_type: str, page_number: int,
                                           total_pages: int, context_info: str, style_genes: str,
                                           unified_design_guide: str, project_topic: str,
                                           project_type: str, project_audience: str, project_style: str) -> str:
        """获取创意模板上下文提示词"""

        # 处理图片信息 - 只有在图片服务启用且有图片信息时才包含
        images_info = ""
        if _is_image_service_enabled() and 'images_summary' in slide_data:
            images_info = f"""


**图片使用要求：**
- 请在HTML中合理使用这些图片资源
- 图片地址已经是绝对地址，可以直接使用
- 根据图片用途、内容描述和实际尺寸选择合适的位置和样式
- 充分利用图片的尺寸信息（宽度x高度）来优化布局设计
- 根据图片文件大小和格式选择合适的显示策略
- 确保图片与页面内容和设计风格协调
- 可以使用CSS对图片进行适当的样式调整（大小、位置、边框等）
"""

        return f"""你是一位富有创意的设计师，需要为第{page_number}页创建一个既保持风格一致性又充满创意的PPT页面。

**严格内容约束**：
- 页面标题：{slide_title}
- 页面类型：{slide_type}
- 总页数：{total_pages}

**完整页面数据参考**：
{slide_data}

{images_info}

**风格模板（页眉和页脚必须完全保持原样）**：
```html
{template_html}
```

**⚠️ 严格保留参考模板的页眉和页脚样式要求 ⚠️**

**绝对不允许修改的区域（除首页、目录页和尾页外）**：
1. **页眉部分**：包括标题位置、字体、颜色、大小、布局等必须与参考模板中所示完全一致
2. **页脚部分**：包括页码、位置、字体、颜色、大小和任何页脚元素必须与参考模板中所示完全一致
3. **模板框架**：页眉和页脚的整体框架结构必须保持完全不变

**允许修改的区域**：
- 仅限页眉和页脚之间的主要内容区域
- 主要内容区域内的布局、颜色、字体可以根据内容需要进行调整
- 主要内容区域内可以添加图片、图表、装饰元素等

**重要说明**：
- 无论生成的幻灯片内容或设计变体如何，页眉和页脚都必须保留原样
- AI生成过程中不应对页眉和页脚模板区域进行任何样式修改
- "完全保持原样"意味着这些区域的所有视觉属性都不能改变

{context_info}

**核心设计原则**

1.  **固定画布**：所有设计都必须在`1280x720`像素的固定尺寸画布内完成。最终页面应水平和垂直居中显示。
2.  **页面专业度**：核心目标是让页面无论内容多少，都显得**专业且设计感强**。
3.  **严格的模板框架保持**：
   - **页眉区域**：标题的位置、字体族、字体大小、字体颜色、字体粗细、对齐方式等必须与参考模板完全一致
   - **页脚区域**：页码的位置、字体族、字体大小、字体颜色、字体粗细、对齐方式等必须与参考模板完全一致
   - **框架结构**：页眉和页脚的容器尺寸、边距、内边距等布局属性必须保持不变
   - **视觉统一**：确保所有页面的页眉和页脚在视觉上完全一致，建立统一的品牌框架

**动态内容自适应布局**

请根据内容的数量，智能地选择最佳布局和字体策略

**视觉呈现与组件运用**

*   **创意优先**：不要局限于简单的文本列表。请根据内容特点，**自由选择并组合最合适的视觉组件**
*   **视觉层次**：通过大小、颜色和布局的变化，建立清晰的视觉焦点和信息层次。

**关键约束**

*   **严禁滚动条**：页面内容**绝对不能溢出**画布。如果内容过多，必须通过上述的**布局调整（如分栏、网格）或适度压缩字体/间距**来解决，而不是出现滚动条。

**核心设计基因（必须保持）**：
{style_genes}

**统一创意设计指导**：
{unified_design_guide}

**项目背景**：
- 主题：{project_topic}
- 类型：{project_type}
- 目标受众：{project_audience}
- PPT风格：{project_style}

**设计哲学**：
1. **一致性原则** - 严格遵循核心设计基因，确保品牌识别度和视觉统一性
2. **创新性原则** - 仅在主要内容区域内进行创新，避免千篇一律但不破坏模板框架
3. **内容适配原则** - 让设计服务于内容，但必须在模板框架约束内进行
4. **用户体验原则** - 优化信息传达效率和视觉舒适度，同时保持模板的专业性

**创意要求**：
- **创意范围**：仅在主要内容区域内进行创意设计，在保持核心设计基因的前提下创造独特的布局结构
- 根据内容特点选择最佳的信息展示方式，但不能影响页眉页脚的原有样式
- 使用创新的视觉元素增强表达效果，但必须限制在主要内容区域内
- 确保每一页都有独特的视觉亮点，同时保持页眉页脚的统一性
- **空间利用创意要求**：
  * 设计时必须考虑内容如何填满整个可用空间
  * 使用flex布局的flex-grow属性让内容区域自动扩展
  * 对于内容较少的页面，通过增大字体、图标、间距等方式充分利用空间
  * 避免所有内容都集中在页面上半部分，要有意识地分布到整个页面
  * 底部区域也要合理利用，可以放置装饰元素或次要信息

**富文本支持**：
- 支持数学公式（使用MathJax）、代码高亮（使用Prism.js）、图表（使用Chart.js）等富文本元素
- 根据内容需要自动添加相应的库和样式

**技术规范**：
- 生成完整的HTML页面（包含<!DOCTYPE html>、head、body）
- 使用Tailwind CSS或内联CSS，确保美观的设计
- 页面尺寸严格控制：html {{ height: 100%; display: flex; align-items: center; justify-content: center; }} body {{ width: 1280px; height: 720px; position: relative; overflow: hidden; }}
- 支持使用Chart.js和Font Awesome库
- 页码显示为：{page_number}/{total_pages}
- **页眉页脚严格保持原样**：
  * 页眉区域的所有HTML结构、CSS样式、字体属性必须与参考模板完全一致
  * 页脚区域的所有HTML结构、CSS样式、字体属性必须与参考模板完全一致
- **空间利用优化**：
  * 主内容区域必须充分利用可用高度（580-620px）
  * 避免内容区域底部出现大量空白
  * 根据内容数量动态调整字体大小和间距
  * 使用flex布局确保内容垂直分布均匀
  * 避免页面内容超出页面底部或顶部
  * 避免页面内容超出页面左右两侧
  * 避免页面内容被遮挡
- 图表容器高度动态设置，确保所有图表内容完全可见且充分利用空间
- 所有内容元素都必须在1280x720范围内，不能出现滚动条

**重要输出格式要求**：
- 必须使用markdown代码块格式返回HTML代码
- 格式：```html\\n[HTML代码]\\n```
- HTML代码必须以<!DOCTYPE html>开始，以</html>结束
- 不要在代码块前后添加任何解释文字
"""

    @staticmethod
    def get_single_slide_html_prompt(slide_data: Dict[str, Any], confirmed_requirements: Dict[str, Any],
                                   page_number: int, total_pages: int, context_info: str,
                                   style_genes: str, unified_design_guide: str, template_html: str) -> str:
        """获取单页HTML生成提示词"""

        # 处理图片信息 - 只有在图片服务启用且有图片信息时才包含
        images_info = ""
        if _is_image_service_enabled() and 'images_summary' in slide_data:
            images_info = f"""

**图片使用要求：**
- 请在HTML中合理使用这些图片资源
- 图片地址已经是绝对地址，可以直接使用
- 根据图片用途、内容描述和实际尺寸选择合适的位置和样式
- 充分利用图片的尺寸信息（宽度x高度）来优化布局设计
- 根据图片文件大小和格式选择合适的显示策略
- 确保图片与页面内容和设计风格协调
- 可以使用CSS对图片进行适当的样式调整（大小、位置、边框等）
"""

        return f"""
根据项目信息，为第{page_number}页生成完整的HTML代码。

项目信息：
- 主题：{confirmed_requirements.get('topic', '')}
- 目标受众：{confirmed_requirements.get('target_audience', '')}
- 其他说明：{confirmed_requirements.get('description', '无')}

当前页面信息：
{slide_data}

{images_info}

**风格模板（页眉和页脚必须完全保持原样）**：
```html
{template_html}
```

**⚠️ 严格保留参考模板的页眉和页脚样式要求 ⚠️**

**绝对不允许修改的区域（除首页、目录页和尾页外）**：
1. **页眉部分**：包括标题位置、字体、颜色、大小、布局等必须与参考模板中所示完全一致
2. **页脚部分**：包括页码、位置、字体、颜色、大小和任何页脚元素必须与参考模板中所示完全一致
3. **模板框架**：页眉和页脚的整体框架结构必须保持完全不变

**允许修改的区域**：
- 仅限页眉和页脚之间的主要内容区域
- 主要内容区域内的布局、颜色、字体可以根据内容需要进行调整
- 主要内容区域内可以添加图片、图表、装饰元素等

**重要说明**：
- 无论生成的幻灯片内容或设计变体如何，页眉和页脚都必须保留原样
- AI生成过程中不应对页眉和页脚模板区域进行任何样式修改
- "完全保持原样"意味着这些区域的所有视觉属性都不能改变

**核心设计原则**

1.  **固定画布**：所有设计都必须在`1280x720`像素的固定尺寸画布内完成。最终页面应水平和垂直居中显示。
2.  **页面专业度**：核心目标是让页面无论内容多少，都显得**专业且设计感强**。
3.  **严格的模板框架保持**：
   - **页眉区域**：标题的位置、字体族、字体大小、字体颜色、字体粗细、对齐方式等必须与参考模板完全一致
   - **页脚区域**：页码的位置、字体族、字体大小、字体颜色、字体粗细、对齐方式等必须与参考模板完全一致
   - **框架结构**：页眉和页脚的容器尺寸、边距、内边距等布局属性必须保持不变
   - **视觉统一**：确保所有页面的页眉和页脚在视觉上完全一致，建立统一的品牌框架

**动态内容自适应布局**

请根据内容的数量，智能地选择最佳布局和字体策略。

**视觉呈现与组件运用**

*   **创意优先**：不要局限于简单的文本列表。请根据内容特点，**自由选择并组合最合适的视觉组件**
*   **视觉层次**：通过大小、颜色和布局的变化，建立清晰的视觉焦点和信息层次。

**关键约束**

*   **严禁滚动条**：页面内容**绝对不能溢出**画布。如果内容过多，必须通过上述的**布局调整（如分栏、网格）或适度压缩字体/间距**来解决，而不是出现滚动条。

{context_info}

**设计平衡要求（一致性与创新并重）**：
1. 使用16:9的响应式PPT尺寸，适配不同屏幕大小
2. **必须保持一致的核心元素**：
   - 遵循提供的设计风格模板中的核心约束
   - 保持主色调和字体系统的统一
   - 维持整体视觉品牌的连贯性
3. **鼓励创新的设计空间**：
   - 根据内容特点创新布局结构
   - 灵活运用视觉元素增强表达效果
   - 适度融入当前设计趋势
   - 优化信息层次和用户体验

**技术规范**：
- 生成完整的HTML页面（包含<!DOCTYPE html>、head、body，不包含style标签）
- 使用Tailwind CSS或内联CSS，确保美观的设计
- 使用16:9响应式设计，适配不同屏幕尺寸
- 使用CSS的aspect-ratio属性保持16:9比例
- 使用clamp()函数实现响应式字体大小
- 使用百分比和vw/vh单位实现响应式布局
- 内容布局清晰，重点突出
- 确保文字清晰可读，颜色搭配协调



{f'''
**图片集成指导**：
- 图片资源: {slide_data.get('image_url', '无')}
- 如果有图片资源，请必须合理地将图片融入页面设计中：
  * 根据图片的实际尺寸（宽度x高度）优化布局和比例设计
  * 考虑图片文件大小，对大文件图片进行适当的压缩显示
  * 根据图片格式（PNG/JPEG/WebP等）选择合适的显示方式
  * 图片大小和位置应与页面布局协调，不影响文字阅读
  * 可以作为背景图、装饰图或内容配图等各种方式使用
  * 确保图片不会导致页面内容溢出或布局混乱
  * 图片应使用响应式设计，适配不同屏幕尺寸

''' if _is_image_service_enabled() else ''}

**富文本支持**：
- 支持数学公式（使用MathJax）、代码高亮（使用Prism.js）、图表（使用Chart.js）等富文本元素
- 根据内容需要自动添加相应的库和样式

**严格的页面尺寸和高度控制**：
- **页面尺寸**：html {{ height: 100%; display: flex; align-items: center; justify-content: center; }} body {{ width: 1280px; height: 720px; position: relative; overflow: hidden; }}
- **内容高度分配**：
  * **页眉区域**：标题的HTML结构、CSS样式、字体族、字体大小、字体颜色、字体粗细、位置、对齐方式等必须与参考模板完全一致，不允许任何修改
  * **页脚区域**：页码的HTML结构、CSS样式、字体族、字体大小、字体颜色、字体粗细、位置、对齐方式等必须与参考模板完全一致，不允许任何修改
  * **主内容区域**：580-620px（充分利用可用空间，根据内容动态调整）- 这是唯一允许修改的区域
  * 避免页面内容超出页面底部或顶部
  * 避免页面内容超出页面左右两侧
  * 避免页面内容被遮挡
- **空间充分利用原则**：
  * **内容自适应扩展**：根据内容数量和类型，动态调整各区域高度
  * **避免大量留白**：合理分配空间，避免底部出现过多空余区域

**核心设计基因（必须保持）**：
{style_genes}

**统一创意设计指导**：
{unified_design_guide}

**重要输出格式要求：**
- 必须使用markdown代码块格式返回HTML代码
- 格式：```html\\n[HTML代码]\\n```
- HTML代码必须以<!DOCTYPE html>开始，以</html>结束
- 不要在代码块前后添加任何解释文字
- 确保代码块标记正确且完整
- 严格遵循上述风格要求生成HTML页面
- **页眉页脚保持原样**：生成的HTML中页眉和页脚部分必须与参考模板完全一致，不允许任何修改
"""

    @staticmethod
    def get_slide_context_prompt(page_number: int, total_pages: int) -> str:
        """获取幻灯片上下文提示词（特殊页面设计要求）"""
        context_parts = []

        if page_number == 1 or page_number == total_pages:
            context_parts.append("**🌟 特殊页面设计要求 🌟**")

            if page_number == 1:
                context_parts.extend([
                    "这是首页，需要在保持原模板风格基础上创造强烈的第一印象。设计原则：",
                    "- **风格一致性**：严格遵循原模板的设计风格、色彩体系、字体选择和布局特征",
                    "- **主题呼应**：确保首页设计与演示主题高度契合，体现专业性和主题相关性",
                    "- **视觉层次**：在原模板框架内运用对比、大小、颜色等手段突出主题标题",
                    "- **背景处理**：基于原模板的背景风格进行适度增强，可考虑渐变、纹理等元素",
                    "- **标题强化**：在保持原模板字体风格的基础上，通过大小、颜色、位置等方式增强表现力",
                    "- **装饰协调**：使用与原模板风格一致的装饰元素，丰富视觉层次但不破坏整体和谐",
                    "- **色彩延续**：严格使用原模板的主色调体系，可适度增加饱和度或亮度来增强吸引力",
                    "- **品牌统一**：确保首页设计与整体演示保持品牌和视觉的统一性"
                ])
            elif page_number == total_pages:
                context_parts.extend([
                    "这是结尾页，需要在保持原模板风格基础上营造完整的收尾感。设计原则：",
                    "- **风格延续**：严格保持与原模板和首页一致的设计风格、色彩和字体体系",
                    "- **主题收尾**：确保结尾页设计与演示主题形成完整呼应，体现主题的完整性",
                    "- **视觉呼应**：与首页和中间页面形成视觉连贯性，保持整体演示的统一感",
                    "- **重点突出**：在原模板框架内突出核心总结信息，确保关键信息得到强调",
                    "- **背景协调**：基于原模板背景风格进行适度处理，营造收尾感但不破坏整体风格",
                    "- **布局平衡**：遵循原模板的布局原则，通过留白和元素分布增强页面的完整感",
                    "- **色彩统一**：严格使用原模板的色彩体系，可适度调整明度来营造收尾氛围",
                    "- **品牌闭环**：确保结尾页与整体演示形成完整的品牌和视觉闭环"
                ])

            context_parts.append("")

        return "\\n".join(context_parts) if context_parts else ""

    @staticmethod
    def get_style_genes_extraction_prompt(template_html: str) -> str:
        """获取设计基因提取提示词"""
        return f"""请分析以下HTML模板，提取其核心设计基因：

**HTML模板：**
```html
{template_html}
```

请从以下维度提取设计基因：

**1. 色彩基因**：
- 主色调和辅助色彩
- 色彩搭配规律
- 色彩的情感表达

**2. 字体基因**：
- 字体族和字号规律
- 字重和字间距特点
- 文字层次结构

**3. 布局基因**：
- 页面结构和比例
- 元素间距和对齐方式
- 空间利用特点

**4. 视觉基因**：
- 装饰元素和图形风格
- 边框和阴影效果
- 视觉重点突出方式

**5. 交互基因**：
- 动效和过渡效果
- 用户体验设计特点
- 响应式设计规律

请提供详细的设计基因分析报告，确保后续页面能够保持一致的设计风格。"""

    @staticmethod
    def get_creative_template_context_prompt(slide_data: Dict[str, Any], template_html: str,
                                           slide_title: str, slide_type: str, page_number: int,
                                           total_pages: int, context_info: str, style_genes: str,
                                           unified_design_guide: str, project_topic: str,
                                           project_type: str, project_audience: str, project_style: str) -> str:
        """获取创意模板上下文提示词"""

        # 处理图片信息 - 只有在图片服务启用且有图片信息时才包含
        images_info = ""
        if _is_image_service_enabled() and 'images_summary' in slide_data:
            images_info = f"""


**图片使用要求：**
- 请在HTML中合理使用已有图片资源
- 图片地址已经是绝对地址，可以直接使用
- 根据图片用途、内容描述和实际尺寸选择合适的位置和样式
- 充分利用图片的尺寸信息（宽度x高度）来优化布局设计
- 根据图片文件大小和格式选择合适的显示策略
- 确保图片与页面内容和设计风格协调
- 可以使用CSS对图片进行适当的样式调整（大小、位置、边框等）
"""
        return f"""你是一位富有创意的设计师，需要为第{page_number}页创建一个既保持风格一致性又充满创意的PPT页面。

**严格内容约束**：
- 页面标题：{slide_title}
- 页面类型：{slide_type}
- 总页数：{total_pages}

**完整页面数据参考**：
{slide_data}

{images_info}

**风格模板（页眉和页脚必须完全保持原样）**：
```html
{template_html}
```

**⚠️ 严格保留参考模板的页眉和页脚样式要求 ⚠️**

**绝对不允许修改的区域（除首页、目录页和尾页外）**：
1. **页眉部分**：包括标题位置、字体、颜色、大小、布局等必须与参考模板中所示完全一致
2. **页脚部分**：包括页码、位置、字体、颜色、大小和任何页脚元素必须与参考模板中所示完全一致
3. **模板框架**：页眉和页脚的整体框架结构必须保持完全不变

**允许修改的区域**：
- 仅限页眉和页脚之间的主要内容区域
- 主要内容区域内的布局、颜色、字体可以根据内容需要进行调整
- 主要内容区域内可以添加图片、图表、装饰元素等

**重要说明**：
- 无论生成的幻灯片内容或设计变体如何，页眉和页脚都必须保留原样
- AI生成过程中不应对页眉和页脚模板区域进行任何样式修改
- "完全保持原样"意味着这些区域的所有视觉属性都不能改变

{context_info}

**核心设计原则**

1.  **固定画布**：所有设计都必须在`1280x720`像素的固定尺寸画布内完成。最终页面应水平和垂直居中显示。
2.  **页面专业度**：核心目标是让页面无论内容多少，都显得**专业且设计感强**。
3.  **严格的模板框架保持**：
   - **页眉区域**：标题的位置、字体族、字体大小、字体颜色、字体粗细、对齐方式等必须与参考模板完全一致
   - **页脚区域**：页码的位置、字体族、字体大小、字体颜色、字体粗细、对齐方式等必须与参考模板完全一致
   - **框架结构**：页眉和页脚的容器尺寸、边距、内边距等布局属性必须保持不变
   - **视觉统一**：确保所有页面的页眉和页脚在视觉上完全一致，建立统一的品牌框架

**动态内容自适应布局**

请根据内容的数量，智能地选择最佳布局和字体策略

**视觉呈现与组件运用**

*   **创意优先**：不要局限于简单的文本列表。请根据内容特点，**自由选择并组合最合适的视觉组件**
*   **视觉层次**：通过大小、颜色和布局的变化，建立清晰的视觉焦点和信息层次。

**关键约束**

*   **严禁滚动条**：页面内容**绝对不能溢出**画布。如果内容过多，必须通过上述的**布局调整（如分栏、网格）或适度压缩字体/间距**来解决，而不是出现滚动条。

**核心设计基因（必须保持）**：
{style_genes}

**统一创意设计指导**：
{unified_design_guide}

**项目背景**：
- 主题：{project_topic}
- 类型：{project_type}
- 目标受众：{project_audience}
- PPT风格：{project_style}

**设计哲学**：
1. **一致性原则** - 严格遵循核心设计基因，确保品牌识别度和视觉统一性
2. **创新性原则** - 仅在主要内容区域内进行创新，避免千篇一律但不破坏模板框架
3. **内容适配原则** - 让设计服务于内容，但必须在模板框架约束内进行
4. **用户体验原则** - 优化信息传达效率和视觉舒适度，同时保持模板的专业性
5. **空间最大化原则** - 在主要内容区域内充分利用页面空间，避免大量留白浪费
   * 内容应该垂直分布均匀，占满主要内容区域的可用高度
   * 使用合适的字体大小和间距，确保内容填充整个主要内容区域


**创意要求**：
- **创意范围**：仅在主要内容区域内进行创意设计，在保持核心设计基因的前提下创造独特的布局结构
- 根据内容特点选择最佳的信息展示方式，但不能影响页眉页脚的原有样式
- 使用创新的视觉元素增强表达效果，但必须限制在主要内容区域内
- 确保每一页都有独特的视觉亮点，同时保持页眉页脚的统一性
- **空间利用创意要求**：
  * 设计时必须考虑内容如何填满整个可用空间
  * 使用flex布局的flex-grow属性让内容区域自动扩展
  * 对于内容较少的页面，通过增大字体、图标、间距等方式充分利用空间
  * 避免所有内容都集中在页面上半部分，要有意识地分布到整个页面
  * 底部区域也要合理利用，可以放置装饰元素或次要信息

{f'''
**图片集成指导**：
- 图片资源: {slide_data.get('image_url', '无')}
- 如果有图片资源，请必须创造性地将图片融入页面设计中：
  * 根据图片的实际尺寸（宽度x高度）进行精确的布局设计
  * 考虑图片文件大小和格式，优化加载性能和显示效果
  * 利用图片的元数据信息（尺寸、格式、大小）进行智能布局
  * 图片大小和位置应与页面布局协调，增强而非干扰内容表达
  * 确保图片使用符合模板的设计基因和风格要求
  * 图片大小和位置应与页面布局协调，不影响文字阅读
  * 可以作为背景图、装饰图或内容配图等各种方式使用
  * 确保图片不会导致页面内容溢出或布局混乱

''' if _is_image_service_enabled() else ''}

**富文本支持**：
- 支持数学公式（使用MathJax）、代码高亮（使用Prism.js）、图表（使用Chart.js）等富文本元素
- 根据内容需要自动添加相应的库和样式

**技术规范**：
- 生成完整的HTML页面（包含<!DOCTYPE html>、head、body）
- 使用Tailwind CSS或内联CSS，确保美观的设计
- 页面尺寸严格控制：html {{ height: 100%; display: flex; align-items: center; justify-content: center; }} body {{ width: 1280px; height: 720px; position: relative; overflow: hidden; }}
- 支持使用Chart.js和Font Awesome库
- 页码显示为：{page_number}/{total_pages}
- **页眉页脚严格保持原样**：
  * 页眉区域的所有HTML结构、CSS样式、字体属性必须与参考模板完全一致
  * 页脚区域的所有HTML结构、CSS样式、字体属性必须与参考模板完全一致
- **空间利用优化**：
  * 主内容区域必须充分利用可用高度（580-620px）
  * 避免内容区域底部出现大量空白
  * 根据内容数量动态调整字体大小和间距
  * 使用flex布局确保内容垂直分布均匀
  * 避免页面内容超出页面底部或顶部
  * 避免页面内容超出页面左右两侧
  * 避免页面内容被遮挡
- 图表容器高度动态设置，确保所有图表内容完全可见且充分利用空间
- 所有内容元素都必须在1280x720范围内，不能出现滚动条

**重要输出格式要求**：
- 必须使用markdown代码块格式返回HTML代码
- 格式：```html\\n[HTML代码]\\n```
- HTML代码必须以<!DOCTYPE html>开始，以</html>结束
- 不要在代码块前后添加任何解释文字
- **页眉页脚保持原样**：生成的HTML中页眉和页脚部分必须与参考模板完全一致，不允许任何修改
"""
