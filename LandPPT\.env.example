# LandPPT Configuration Example
# Copy this file to .env and configure your settings

# =============================================================================
# AI Provider Configuration
# =============================================================================

# Default AI provider to use (openai, anthropic, google, azure_openai, ollama)
# For production use, configure one of the real AI providers
DEFAULT_AI_PROVIDER=openai

# OpenAI Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_BASE_URL=https://api.openai.com/v1

# Anthropic Configuration
# Get your API key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-haiku-20240307

# Google Gemini Configuration
# Get your API key from: https://aistudio.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_MODEL=gemini-1.5-flash

# Azure OpenAI Configuration
# Set up Azure OpenAI service and get your credentials
AZURE_OPENAI_API_KEY=your_azure_openai_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Ollama Configuration (for local models)
# Install Ollama from: https://ollama.ai/
# Then run: ollama pull llama2
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# Tavily API Configuration (for research functionality)
# Get your API key from: https://tavily.com/
TAVILY_API_KEY=your_tavily_api_key_here
TAVILY_MAX_RESULTS=10
TAVILY_SEARCH_DEPTH=advanced
# Optional: Comma-separated domains to include/exclude
# TAVILY_INCLUDE_DOMAINS=wikipedia.org,arxiv.org
# TAVILY_EXCLUDE_DOMAINS=example.com

# SearXNG Configuration (for research functionality)
# SearXNG instance host URL (e.g., http://localhost:8888 or https://searx.example.com)
SEARXNG_HOST=
SEARXNG_MAX_RESULTS=10
SEARXNG_LANGUAGE=auto
SEARXNG_TIMEOUT=30

# Research Configuration
# Available providers: tavily, searxng, both
RESEARCH_PROVIDER=tavily
# Enable deep content extraction from web pages
RESEARCH_ENABLE_CONTENT_EXTRACTION=true
# Maximum content length to extract from each page
RESEARCH_MAX_CONTENT_LENGTH=5000
# Timeout for content extraction requests (seconds)
RESEARCH_EXTRACTION_TIMEOUT=30

# PDF to PPTX Conversion Configuration
# Apryse SDK License Key for PDF to PowerPoint conversion
# Get your license key from: https://docs.apryse.com/
APRYSE_LICENSE_KEY=your_apryse_license_key_here

# SiliconFlow API Configuration (for AI image generation)
# Get your API key from: https://siliconflow.cn/
SILICONFLOW_API_KEY=your_siliconflow_api_key_here

# Pixabay API Configuration (for network image search)
# Get your API key from: https://pixabay.com/api/docs/
PIXABAY_API_KEY=your_pixabay_api_key_here

# Unsplash API Configuration (for network image search)
# Get your access key from: https://unsplash.com/developers
UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here

# Pollinations API Configuration (for AI image generation)
# Pollinations is a free service, but tokens provide additional features
POLLINATIONS_API_BASE=https://image.pollinations.ai
# Optional API token for removing logo and higher limits
POLLINATIONS_API_TOKEN=your_pollinations_token_here
# Optional referrer identifier for authentication and analytics
POLLINATIONS_REFERRER=LandPPT
# Model selection: flux (default, high quality), turbo (fast), gptimage (supports transparency)
POLLINATIONS_MODEL=flux

# =============================================================================
# AI Generation Parameters
# =============================================================================

# Maximum tokens to generate (adjust based on your needs and costs)
MAX_TOKENS=8192

# Temperature for generation (0.0 = deterministic, 1.0 = creative)
TEMPERATURE=0.7

# Top-p sampling parameter (nucleus sampling)
TOP_P=1.0

# =============================================================================
# Application Configuration
# =============================================================================

# Server settings
HOST=0.0.0.0
PORT=8000
DEBUG=false
RELOAD=false

# Security settings
SECRET_KEY=your-very-secure-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=300

# File upload settings
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# Cache settings
CACHE_TTL=3600

# Database settings (for future use)
DATABASE_URL=sqlite:///./landppt.db

# =============================================================================
# Feature Flags
# =============================================================================

# Enable network mode for enhanced content (future feature)
ENABLE_NETWORK_MODE=true

# Enable local model support
ENABLE_LOCAL_MODELS=false

# Enable streaming responses (future feature)
ENABLE_STREAMING=true

# =============================================================================
# Image Service Configuration
# =============================================================================

# Enable image service functionality
ENABLE_IMAGE_SERVICE=false

# Enable local image hosting
ENABLE_LOCAL_IMAGES=false

# Enable network image search
ENABLE_NETWORK_SEARCH=true

# Enable AI image generation
ENABLE_AI_GENERATION=false

# Smart image selection for local images
LOCAL_IMAGES_SMART_SELECTION=true

# Enable smart image selection across all sources
ENABLE_SMART_IMAGE_SELECTION=true

# Maximum images per slide configuration
MAX_LOCAL_IMAGES_PER_SLIDE=2
MAX_NETWORK_IMAGES_PER_SLIDE=1
MAX_AI_IMAGES_PER_SLIDE=2
MAX_TOTAL_IMAGES_PER_SLIDE=1

# Default providers
# Available AI image providers: dalle, siliconflow, pollinations
DEFAULT_AI_IMAGE_PROVIDER=pollinations
DEFAULT_NETWORK_SEARCH_PROVIDER=pixabay

# SearXNG Configuration
SEARXNG_HOST=

# AI image generation quality (standard, high)
AI_IMAGE_QUALITY=standard

# =============================================================================
# Logging Configuration
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Log AI requests and responses (be careful with sensitive data)
# Set to true only for debugging, false for production
LOG_AI_REQUESTS=false
