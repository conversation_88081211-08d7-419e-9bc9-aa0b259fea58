{"template_name": "宣纸风", "description": "传统宣纸的水墨质感，营造出一种典雅而又富有变化的视觉风格。", "html_template": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ page_title }}</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js\"></script>\n    <style>\n        /* --- 基础与背景设置 --- */\n        html {\n            height: 100%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background-color: #f4f1ea; /* 宣纸底色 */\n        }\n\n        body {\n            width: 1280px;\n            height: 720px;\n            margin: 0;\n            padding: 0;\n            position: relative;\n            overflow: hidden;\n            font-family: '<PERSON><PERSON> SC', '<PERSON><PERSON>ait<PERSON>', '<PERSON><PERSON><PERSON>', '楷体', '<PERSON> YaHei', '<PERSON><PERSON><PERSON> SC', sans-serif;\n            flex-shrink: 0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background-image: url('https://www.transparenttextures.com/patterns/rice-paper-3.png'); /* 宣纸纹理 */\n            background-color: #fdfaf3;\n        }\n\n        body::before {\n            content: '';\n            position: absolute;\n            top: -20%; left: -20%; width: 140%; height: 140%;\n            background: \n                radial-gradient(circle at 20% 80%, rgba(200, 160, 100, 0.15), transparent 50%),\n                radial-gradient(circle at 75% 30%, rgba(130, 150, 200, 0.1), transparent 50%);\n            z-index: 1;\n            animation: liquid-flow-outer 30s ease-in-out infinite alternate;\n            mix-blend-mode: multiply; /* 混合模式，让光晕更好地融入背景 */\n        }\n        \n        @keyframes liquid-flow-outer {\n            from { transform: rotate(0deg) scale(1.2); filter: blur(100px); }\n            to   { transform: rotate(180deg) scale(1.5); filter: blur(120px); }\n        }\n\n        .slide-container {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            flex-direction: column;\n            color: #333; /* 主文字颜色改为深墨色 */\n            position: relative;\n            z-index: 3; \n            background: rgba(253, 250, 243, 0.85); /* 半透明宣纸背景 */\n            backdrop-filter: blur(10px) saturate(120%);\n            -webkit-backdrop-filter: blur(10px) saturate(120%);\n            border-radius: 8px; /* 稍微硬朗的边角 */\n            border: 1px solid rgba(0, 0, 0, 0.1);\n            box-shadow: \n                0 15px 35px rgba(50, 50, 93, 0.1), \n                0 5px 15px rgba(0, 0, 0, 0.07);\n        }\n        \n        .slide-header {\n            padding: 40px 60px 20px 60px;\n            border-bottom: 2px solid rgba(0, 0, 0, 0.1); /* 水墨风格的分割线 */\n        }\n        \n        .slide-title {\n            font-size: clamp(2.5rem, 4vw, 4rem);\n            font-weight: 600; \n            color: #2c2c2c; /* 标题颜色 */\n            margin: 0;\n            line-height: 1.3;\n            text-shadow: 1px 1px 2px rgba(0,0,0,0.05);\n        }\n        \n        .slide-content {\n            flex: 1;\n            padding: 30px 60px;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            max-height: 580px;\n            overflow: auto;\n        }\n        \n        .content-main {\n            font-size: clamp(1.2rem, 2.5vw, 1.6rem);\n            line-height: 1.8; \n            color: #444;\n        }\n        \n        .content-points {\n            list-style: none; padding: 0; margin: 20px 0 0 0;\n        }\n        \n        .content-points li {\n            margin-bottom: 20px; padding-left: 40px; position: relative; font-size: 1.35rem;\n        }\n        \n        /* 列表符号: \"印章\" 效果 */\n        .content-points li:before {\n            content: \"\\25A0\"; /* 方块字符，模拟印章形状 */\n            font-family: 'SimSun', '宋体'; /* 使用宋体以获得更方正的字符 */\n            position: absolute;\n            left: 5px; top: 8px;\n            width: 18px; height: 18px;\n            font-size: 20px;\n            line-height: 18px;\n            color: crimson; /* 朱砂红 */\n            transform: rotate(5deg); /* 微微旋转，如同手盖印章 */\n            opacity: 0.85;\n            text-align: center;\n        }\n        \n        .slide-footer {\n            position: absolute;\n            bottom: 25px;\n            right: 40px;\n            font-size: 16px;\n            color: rgba(0, 0, 0, 0.4);\n            font-weight: 600;\n        }\n        \n        /* --- 特殊组件样式 --- */\n        .highlight-box {\n            background: rgba(220, 53, 69, 0.05); /* 朱砂红的淡淡背景 */\n            border-left: 4px solid crimson;\n            padding: 20px 25px;\n            margin: 20px 0;\n            border-radius: 0 8px 8px 0;\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 25px;\n            margin: 20px 0;\n        }\n        \n        /* 小卡片: \"毛笔字帖\" 效果 */\n        .stat-card {\n            background: transparent;\n            padding: 25px 20px;\n            text-align: center;\n            border: 2px solid rgba(0, 0, 0, 0.2);\n            border-radius: 4px 12px 4px 8px; /* 不规则圆角，模拟笔触 */\n            transition: all 0.3s ease-in-out;\n        }\n\n        .stat-card:hover {\n            transform: translateY(-5px);\n            background: rgba(0,0,0,0.03);\n            box-shadow: 0 4px 10px rgba(0,0,0,0.1);\n            border-color: crimson;\n        }\n        \n        .stat-number {\n            font-size: 3rem;\n            font-weight: 700;\n            color: #333;\n            display: block;\n        }\n        \n        .stat-label {\n            font-size: 1rem;\n            color: rgba(0, 0, 0, 0.6);\n            margin-top: 5px;\n        }\n        \n        /* 响应式调整 */\n        @media (max-width: 1280px) {\n            body {\n                width: 95vw;\n                height: 53.4375vw; /* 保持16:9比例 */\n                max-height: 95vh;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"slide-container\">\n        <div class=\"slide-header\">\n            <h1 class=\"slide-title\">{{ main_heading }}</h1>\n        </div>\n        \n        <div class=\"slide-content\">\n            <div class=\"content-main\">\n                {{ page_content }}\n            </div>\n        </div>\n        \n        <div class=\"slide-footer\">\n            {{ current_page_number }} / {{ total_page_count }}\n        </div>\n    </div>\n</body>\n</html>", "tags": ["宣纸", "国风", "水墨", "典雅"], "is_default": false, "export_info": {"exported_at": "2025-07-23T08:06:24.463Z", "original_id": 11, "original_created_at": 1753257751.6069405}}