{"template_name": "中国泼墨风", "description": "中国传统水墨画的质感和意境", "html_template": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ page_title }}</title>\n    <!-- 移除了 Tailwind 和 Font Awesome, 因为样式是完全自定义的 -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <style>\n        /* 引入具有书法韵味的在线字体 (可选，但效果更佳) */\n        @import url('https://fonts.googleapis.com/css2?family=Ma+<PERSON>+<PERSON>&family=ZCOOL+KuaiLe&display=swap');\n\n        html {\n            height: 100%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            /* 使用深色背景衬托宣纸效果 */\n            background-color: #1a1a1a; \n        }\n\n        body {\n            width: 1280px;\n            height: 720px;\n            margin: 0;\n            padding: 0;\n            position: relative;\n            overflow: hidden;\n            /* 宣纸纹理背景 */\n            background-color: #f7f3e9;\n            background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100\" height=\"100\" viewBox=\"0 0 100 100\"><filter id=\"n\" x=\"0\" y=\"0\"><feTurbulence type=\"fractalNoise\" baseFrequency=\"0.8\" numOctaves=\"6\" stitchTiles=\"stitch\"/></filter><rect width=\"100\" height=\"100\" filter=\"url(%23n)\" opacity=\"0.1\"/></svg>');\n            /* 字体设定，优先使用有书法感的字体 */\n            font-family: 'KaiTi', 'STKaiti', 'SimSun', serif;\n            flex-shrink: 0; \n            box-shadow: 0 10px 30px rgba(0,0,0,0.5);\n        }\n        \n        .slide-container {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            flex-direction: column;\n            color: #333; /* 墨色 */\n            position: relative;\n        }\n\n        /* 左上角的墨迹装饰 */\n        .slide-container::before {\n            content: '';\n            position: absolute;\n            top: -20px;\n            left: -30px;\n            width: 200px;\n            height: 200px;\n            background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><path d=\"M 50,0 C 20,10 10,40 0,50 C 10,80 40,90 50,100 C 80,90 90,60 100,50 C 90,20 60,10 50,0 Z\" fill=\"rgba(0,0,0,0.08)\" transform=\"rotate(45 50 50) scale(1.2)\" /></svg>');\n            background-repeat: no-repeat;\n            background-size: contain;\n            opacity: 0.5;\n            z-index: 0;\n        }\n        \n        .slide-header {\n            padding: 50px 70px 20px 70px;\n            position: relative;\n        }\n        \n        .slide-title {\n            /* 标题使用更具设计感的书法字体 */\n            font-family: 'Ma Shan Zheng', 'STKaiti', 'KaiTi', cursive;\n            font-size: clamp(3rem, 5vw, 4.5rem);\n            font-weight: normal; /* 书法字体不需加粗 */\n            color: #1a1a1a;\n            margin: 0;\n            line-height: 1.2;\n            max-height: 100px;\n            overflow: hidden;\n            z-index: 1;\n            position: relative;\n        }\n\n        /* 标题下的毛笔笔触分割线 */\n        .slide-header::after {\n            content: '';\n            position: absolute;\n            left: 70px;\n            bottom: 5px;\n            width: 300px;\n            height: 15px;\n            background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 200 10\"><path d=\"M 0,5 C 30,2 70,8 100,5 C 130,2 170,8 200,5\" stroke=\"%23990000\" stroke-width=\"3\" fill=\"none\" stroke-linecap=\"round\" /></svg>');\n            background-repeat: no-repeat;\n            background-size: 100% 100%;\n            opacity: 0.7;\n        }\n        \n        .slide-content {\n            flex: 1;\n            padding: 20px 70px;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            max-height: 550px;\n            overflow: auto; /* 内容多时可滚动 */\n        }\n        \n        .content-main {\n            font-size: clamp(1.2rem, 2.5vw, 1.6rem);\n            line-height: 2; /* 增加行距，更具古籍感 */\n            color: #3d3d3d; /* 深墨色 */\n        }\n        \n        .content-points {\n            list-style: none;\n            padding: 0;\n            margin: 0;\n        }\n        \n        .content-points li {\n            margin-bottom: 20px;\n            padding-left: 35px;\n            position: relative;\n            font-size: clamp(1.1rem, 2.5vw, 1.5rem);\n            line-height: 1.8;\n        }\n        \n        /* 列表项标记替换为朱红色圆点，如朱批 */\n        .content-points li:before {\n            content: \"●\";\n            position: absolute;\n            left: 0;\n            top: 0.1em;\n            color: #a91e22; /* 朱红色 */\n            font-size: 0.8em;\n        }\n        \n        /* 仿印章效果的页脚 */\n        .slide-footer {\n            position: absolute;\n            bottom: 30px;\n            right: 40px;\n            width: 80px;\n            height: 30px;\n            background: #c0392b; /* 印泥红 */\n            color: #f7f3e9; /* 纸白色，阴文效果 */\n            font-size: 16px;\n            font-weight: 600;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            border: 2px solid #a91e22;\n            border-radius: 2px;\n            /* 使用篆体感的字体，若无则用楷体 */\n            font-family: 'ZCOOL KuaiLe', 'KaiTi', sans-serif; \n            box-shadow: 1px 1px 3px rgba(0,0,0,0.2);\n        }\n        \n        .chart-container {\n            max-height: 300px;\n            margin: 20px 0;\n            border: 1px solid rgba(0,0,0,0.1);\n            padding: 10px;\n            background: rgba(0,0,0,0.02);\n        }\n        \n        /* 重点内容框：仿传统竖批样式 */\n        .highlight-box {\n            background: rgba(153,0,0, 0.03); /* 极淡的朱砂背景 */\n            border-right: 3px solid #c0392b; /* 右侧朱红竖线 */\n            border-left: none;\n            padding: 20px 30px 20px 20px;\n            margin: 20px 0;\n            border-radius: 0;\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 30px;\n            margin: 20px 0;\n        }\n        \n        /* 数据卡片：淡墨描边效果 */\n        .stat-card {\n            background: transparent;\n            padding: 20px;\n            border-radius: 4px;\n            text-align: center;\n            border: 1px solid rgba(0, 0, 0, 0.2);\n            border-style: dashed;\n        }\n        \n        .stat-number {\n            font-size: 2.8rem;\n            font-weight: bold;\n            color: #c0392b; /* 朱红色突出数字 */\n            display: block;\n            font-family: 'Microsoft YaHei', sans-serif; /* 数字用清晰字体 */\n        }\n        \n        .stat-label {\n            font-size: 1.1rem;\n            color: #555; /* 墨色 */\n            margin-top: 5px;\n        }\n        \n        @media (max-width: 1280px) {\n            body {\n                width: 100vw;\n                height: 56.25vw;\n                max-height: 100vh;\n            }\n        }\n\n        /* Chart.js 图表颜色适配，需要在 JS 中配置 */\n        /* 这是一个示例，实际颜色需要在创建图表的 JS 代码中设置 */\n        /*\n        const options = {\n            scales: { ... },\n            plugins: { ... },\n            elements: {\n                bar: {\n                    backgroundColor: 'rgba(192, 57, 43, 0.6)',\n                    borderColor: '#c0392b',\n                    borderWidth: 1\n                },\n                point: {\n                    backgroundColor: '#c0392b',\n                    borderColor: '#a91e22'\n                },\n                line: {\n                    borderColor: 'rgba(61, 61, 61, 0.7)'\n                }\n            }\n        };\n        */\n    </style>\n</head>\n<body>\n    <div class=\"slide-container\">\n        <div class=\"slide-header\">\n            <h1 class=\"slide-title\">{{ main_heading }}</h1>\n        </div>\n        \n        <div class=\"slide-content\">\n            <div class=\"content-main\">\n                <!-- 这里是您的页面内容，可以是文本、列表、图表等 -->\n                <!-- 示例：普通文本 -->\n                <p>夫天地者，万物之逆旅也；光阴者，百代之过客也。而浮生若梦，为欢几何？</p>\n\n                <!-- 示例：高亮框 -->\n                <div class=\"highlight-box\">\n                    此为重点内容，以朱笔批注之。引经据典，发人深省。\n                </div>\n\n                <!-- 示例：列表 -->\n                <ul class=\"content-points\">\n                    <li>其一：格物致知，诚意正心。</li>\n                    <li>其二：修身齐家，治国平天下。</li>\n                    <li>其三：道法自然，天人合一。</li>\n                </ul>\n\n                <!-- 示例：数据统计 -->\n                <div class=\"stats-grid\">\n                    <div class=\"stat-card\">\n                        <span class=\"stat-number\">三千</span>\n                        <span class=\"stat-label\">弟子</span>\n                    </div>\n                    <div class=\"stat-card\">\n                        <span class=\"stat-number\">七十二</span>\n                        <span class=\"stat-label\">贤人</span>\n                    </div>\n                    <div class=\"stat-card\">\n                        <span class=\"stat-number\">一部</span>\n                        <span class=\"stat-label\">论语</span>\n                    </div>\n                </div>\n\n                 <!-- \n                    您可以取消这里的注释来展示一个图表\n                    <div class=\"chart-container\">\n                        <canvas id=\"myChart\"></canvas>\n                    </div> \n                -->\n\n            </div>\n        </div>\n        \n        <div class=\"slide-footer\">\n            {{ current_page_number }} / {{ total_page_count }}\n        </div>\n    </div>\n\n    <!-- \n    <script>\n        // 如果您使用图表，请在这里初始化\n        const ctx = document.getElementById('myChart');\n        if (ctx) {\n            new Chart(ctx, {\n                type: 'bar', // or 'line', 'pie', etc.\n                data: {\n                    labels: ['甲', '乙', '丙', '丁', '戊', '己'],\n                    datasets: [{\n                        label: '数据展示',\n                        data: [12, 19, 3, 5, 2, 3],\n                        backgroundColor: 'rgba(169, 30, 34, 0.4)', // 朱红，半透明\n                        borderColor: 'rgba(169, 30, 34, 1)', // 朱红，不透明\n                        borderWidth: 1\n                    }]\n                },\n                options: {\n                    scales: {\n                        y: {\n                            beginAtZero: true,\n                            grid: {\n                                color: 'rgba(0, 0, 0, 0.08)' // 淡墨色网格线\n                            },\n                            ticks: {\n                                color: '#555' // 墨色刻度\n                            }\n                        },\n                        x: {\n                           grid: {\n                                display: false // 隐藏X轴网格线\n                            },\n                            ticks: {\n                                color: '#555'\n                            }\n                        }\n                    },\n                    plugins: {\n                        legend: {\n                            labels: {\n                                color: '#333' // 图例文字颜色\n                            }\n                        }\n                    }\n                }\n            });\n        }\n    </script>\n    -->\n</body>\n</html>", "tags": ["中国风", "泼墨", "传统"], "is_default": false, "export_info": {"exported_at": "2025-06-28T10:14:11.597Z", "original_id": 5, "original_created_at": 1751075831.969087}}