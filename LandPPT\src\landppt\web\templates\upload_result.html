{% extends "base.html" %}

{% block title %}上传结果 - LandPPT{% endblock %}

{% block content %}
{% if success %}
<div class="alert alert-success">
    <h3 style="margin-bottom: 10px;">🎉 文件上传成功！</h3>
    <p>您的文件已成功上传并处理，可以基于此内容生成 PPT。</p>
</div>

<div style="text-align: center; margin-bottom: 30px;">
    <h2 style="color: #2c3e50; margin-bottom: 20px;">📁 文件处理结果</h2>
</div>

<div class="grid">
    <div class="card">
        <h3 style="color: #3498db; margin-bottom: 15px;">📋 文件信息</h3>
        <div style="color: #7f8c8d; line-height: 1.8;">
            <p><strong>文件名:</strong> {{ filename }}</p>
            <p><strong>文件类型:</strong> {{ type }}</p>
            <p><strong>文件大小:</strong> {{ (size / 1024) | round(2) }} KB</p>
            <p><strong>处理状态:</strong> <span style="color: #27ae60;">✅ 成功</span></p>
        </div>
    </div>
    
    <div class="card">
        <h3 style="color: #27ae60; margin-bottom: 15px;">🎯 下一步操作</h3>
        <p style="color: #7f8c8d; margin-bottom: 20px;">
            文件内容已提取完成，您可以基于此内容创建 PPT
        </p>
        <div style="text-align: center;">
            <a href="/scenarios" class="btn btn-primary" style="width: 100%; margin-bottom: 10px;">
                🚀 基于此内容创建 PPT
            </a>
            <a href="/" class="btn" style="background: #95a5a6; width: 100%;">
                🏠 返回首页
            </a>
        </div>
    </div>
</div>

{% if processed_content %}
<div style="margin-top: 30px;">
    <h3 style="color: #2c3e50; margin-bottom: 20px;">📄 提取的内容预览</h3>
    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; border-left: 4px solid #3498db;">
        <div style="max-height: 400px; overflow-y: auto; font-family: 'Courier New', monospace; line-height: 1.6; color: #2c3e50;">
            {{ processed_content | replace('\n', '<br>') | safe }}
        </div>
        {% if processed_content | length > 500 %}
        <p style="margin-top: 15px; color: #7f8c8d; font-style: italic;">
            * 显示前 500 个字符，完整内容已保存用于 PPT 生成
        </p>
        {% endif %}
    </div>
</div>
{% endif %}

<div style="margin-top: 40px; padding: 30px; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 15px;">
    <h3 style="color: #2c3e50; margin-bottom: 20px; text-align: center;">💡 使用建议</h3>
    
    <div class="grid">
        <div style="text-align: center;">
            <div style="font-size: 3em; margin-bottom: 10px;">🎯</div>
            <h4 style="color: #3498db; margin-bottom: 10px;">选择合适场景</h4>
            <p style="color: #7f8c8d;">根据文档内容选择最匹配的 PPT 场景模板</p>
        </div>
        
        <div style="text-align: center;">
            <div style="font-size: 3em; margin-bottom: 10px;">✏️</div>
            <h4 style="color: #27ae60; margin-bottom: 10px;">补充要求</h4>
            <p style="color: #7f8c8d;">在生成时添加具体要求，如目标受众、重点内容等</p>
        </div>
        
        <div style="text-align: center;">
            <div style="font-size: 3em; margin-bottom: 10px;">🔄</div>
            <h4 style="color: #f39c12; margin-bottom: 10px;">迭代优化</h4>
            <p style="color: #7f8c8d;">生成后可以重新调整和优化 PPT 内容</p>
        </div>
    </div>
</div>

{% else %}
<div class="alert alert-error">
    <h3 style="margin-bottom: 10px;">❌ 文件上传失败</h3>
    <p>很抱歉，文件处理过程中出现了错误。</p>
    {% if error %}
    <div style="margin-top: 15px; padding: 10px; background: rgba(231, 76, 60, 0.1); border-radius: 5px;">
        <strong>错误详情:</strong> {{ error }}
    </div>
    {% endif %}
</div>

<div style="text-align: center; margin-top: 30px;">
    <a href="/web" class="btn btn-primary">🔄 重新尝试</a>
    <a href="/web" class="btn" style="background: #95a5a6; margin-left: 10px;">🏠 返回首页</a>
</div>

<div style="margin-top: 40px; padding: 30px; background: #e8f4fd; border-radius: 15px; border-left: 4px solid #3498db;">
    <h4 style="color: #2c3e50; margin-bottom: 15px;">📋 支持的文件格式</h4>
    <div class="grid">
        <div style="text-align: center;">
            <div style="font-size: 2em; margin-bottom: 5px;">📄</div>
            <strong>.docx</strong>
            <p style="color: #7f8c8d; font-size: 0.9em;">Word 文档</p>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 2em; margin-bottom: 5px;">📕</div>
            <strong>.pdf</strong>
            <p style="color: #7f8c8d; font-size: 0.9em;">PDF 文档</p>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 2em; margin-bottom: 5px;">📝</div>
            <strong>.txt</strong>
            <p style="color: #7f8c8d; font-size: 0.9em;">纯文本文件</p>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 2em; margin-bottom: 5px;">📋</div>
            <strong>.md</strong>
            <p style="color: #7f8c8d; font-size: 0.9em;">Markdown 文档</p>
        </div>
    </div>
    
    <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.7); border-radius: 8px;">
        <h5 style="color: #2c3e50; margin-bottom: 10px;">💡 上传提示</h5>
        <ul style="color: #7f8c8d; text-align: left; line-height: 1.6;">
            <li>文件大小建议不超过 10MB</li>
            <li>确保文档内容清晰、结构完整</li>
            <li>PDF 文件请确保文字可以正常选择和复制</li>
            <li>Word 文档建议使用标准格式，避免复杂排版</li>
        </ul>
    </div>
</div>
{% endif %}
{% endblock %}
