{"template_name": "中式书卷风", "description": "融合了古典书卷的雅致与现代排版的清晰。模拟卷轴的边框和水墨质感，营造出沉静、典雅的阅读体验。", "html_template": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ page_title }}</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js\"></script>\n    <style>\n        html, body {\n            width: 100%;\n            height: 100%;\n            margin: 0;\n            padding: 0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background-color: #383028;\n        }\n\n        body {\n            background-image: url('https://www.transparenttextures.com/patterns/subtle-wood.png');\n        }\n\n        .slide-container {\n            width: 1280px;\n            height: 720px;\n            display: flex;\n            flex-direction: column;\n            color: #4a3c2b;\n            position: relative;\n            background-color: #f7f3e9; \n            background-image: url('https://www.transparenttextures.com/patterns/rice-paper-3.png'); \n            box-shadow: 0 15px 50px rgba(0,0,0,0.6);\n            border: 1px solid #dcd3bf;\n        }\n\n        .slide-container::before, .slide-container::after {\n            content: '';\n            position: absolute;\n            top: -10px;\n            bottom: -10px;\n            width: 25px;\n            background-color: #6a5a4a;\n            background-image: linear-gradient(to right, rgba(0,0,0,0.2), transparent, rgba(0,0,0,0.2));\n            border-radius: 5px;\n            box-shadow: 0 0 10px rgba(0,0,0,0.5);\n            z-index: -1;\n        }\n\n        .slide-container::before {\n            left: -35px;\n        }\n        .slide-container::after {\n            right: -35px;\n        }\n        \n        .slide-header {\n            padding: 40px 60px 20px 60px;\n            border-bottom: 1px solid rgba(140, 120, 93, 0.2);\n            flex-shrink: 0;\n        }\n        \n        .slide-title {\n            font-family: 'Songti SC', 'STSong', 'SimSun', '宋体', 'Georgia', serif;\n            font-size: clamp(2.5rem, 4vw, 3.5rem);\n            font-weight: 600;\n            line-height: 1.4;\n            text-align: left;\n            color: #5a4832;\n        }\n        \n        .slide-content {\n            flex-grow: 1;\n            padding: 30px 60px;\n            overflow-y: auto;\n            font-family: 'Songti SC', 'STSong', 'SimSun', '宋体', 'Helvetica Neue', 'Arial', sans-serif;\n        }\n\n        .slide-content::-webkit-scrollbar {\n            width: 10px;\n        }\n\n        .slide-content::-webkit-scrollbar-track {\n            background: transparent;\n        }\n\n        .slide-content::-webkit-scrollbar-thumb {\n            background-color: #b0a08c;\n            border-radius: 5px;\n        }\n        \n        .content-main {\n            font-size: clamp(1.1rem, 2.5vw, 1.4rem);\n            line-height: 1.9; \n            color: #4a3c2b;\n        }\n        \n        .content-points {\n            list-style: none; padding: 0; margin: 25px 0 0 0;\n        }\n        \n        .content-points li {\n            margin-bottom: 20px; padding-left: 35px; position: relative; font-size: 1.25rem;\n        }\n        \n        .content-points li::before {\n            content: \"●\";\n            position: absolute;\n            left: 0; top: 10px;\n            font-size: 16px;\n            color: #d9534f;\n            opacity: 0.7;\n        }\n        \n        .slide-footer {\n            padding: 10px 60px;\n            text-align: right;\n            font-family: 'Georgia', serif;\n            font-size: 16px;\n            color: rgba(74, 60, 43, 0.6);\n            flex-shrink: 0;\n        }\n        \n        .highlight-box {\n            background: rgba(217, 83, 79, 0.05);\n            border-left: 3px solid #d9534f;\n            padding: 20px 25px;\n            margin: 20px 0;\n            border-radius: 2px;\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 25px;\n            margin: 20px 0;\n        }\n        \n        .stat-card {\n            background: rgba(247, 243, 233, 0.5);\n            padding: 25px 20px;\n            text-align: center;\n            border: 1px solid #dcd3bf;\n            border-radius: 4px;\n            transition: all 0.3s ease-out;\n        }\n\n        .stat-card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n            border-color: #8c785d;\n        }\n        \n        .stat-number {\n            font-family: 'Georgia', 'Times New Roman', serif;\n            font-size: 3rem;\n            font-weight: 600;\n            color: #5a4832;\n            display: block;\n        }\n        \n        .stat-label {\n            font-family: 'Songti SC', 'STSong', 'SimSun', '宋体', sans-serif;\n            font-size: 1rem;\n            color: #8c785d;\n            margin-top: 5px;\n        }\n\n    </style>\n</head>\n<body>\n    <div class=\"slide-container\">\n        <div class=\"slide-header\">\n            <h1 class=\"slide-title\">{{ main_heading }}</h1>\n        </div>\n        \n        <div class=\"slide-content\">\n            <div class=\"content-main\">\n                {{ page_content }}\n            </div>\n        </div>\n\n        <div class=\"slide-footer\">\n            {{ current_page_number }} / {{ total_page_count }}\n        </div>\n    </div>\n</body>\n</html>", "tags": ["书卷", "中式", "复古", "古典", "宋体", "典雅"], "is_default": false, "export_info": {"exported_at": "2025-07-23T09:20:00.000Z", "original_id": null, "original_created_at": null}}