{"template_name": "终端风", "description": "终端风", "html_template": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ page_title }}</title>\n    <style>\n        :root {\n            --terminal-bg: #0a100c; \n            --terminal-text: #3ddc84; \n            --terminal-text-darker: #2a9d62; \n            --terminal-glow: rgba(61, 220, 132, 0.25); \n            \n            \n            --glitch-color-1: #c7347e; \n            --glitch-color-2: #3e8ad6; \n        }\n\n        @keyframes scanline {\n            0% { background-position: 0 0; }\n            100% { background-position: 0 100%; }\n        }\n\n        @keyframes flicker {\n            0%, 100% { opacity: 1; }\n            50% { opacity: 0.98; } \n        }\n\n        html {\n            height: 100%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background-color: #000;\n        }\n\n        body {\n            width: 1280px;\n            height: 720px;\n            margin: 0;\n            padding: 0;\n            position: relative;\n            overflow: hidden;\n            background: var(--terminal-bg);\n            font-family: 'Fira Code', 'Operator Mono', 'Courier New', Courier, monospace;\n            flex-shrink: 0; \n        }\n        \n        \n        .slide-container {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            flex-direction: column;\n            color: var(--terminal-text);\n            position: relative;\n            border: 2px solid var(--terminal-text-darker); \n            border-radius: 4px; \n            box-shadow: 0 0 15px var(--terminal-glow), inset 0 0 80px rgba(0,0,0,0.8);\n            animation: flicker 0.2s infinite;\n        }\n        \n        .slide-container::before {\n            content: \" \";\n            display: block;\n            position: absolute;\n            top: 0; left: 0; bottom: 0; right: 0;\n            background: linear-gradient(rgba(18, 16, 16, 0) 50%, rgba(0, 0, 0, 0.2) 50%);\n            z-index: 2;\n            background-size: 100% 3px;\n            pointer-events: none;\n            animation: scanline 20s linear infinite;\n        }\n        \n        .slide-header {\n            padding: 40px 60px 20px 60px;\n            border-bottom: 1px solid var(--terminal-text-darker);\n            text-shadow: 0 0 4px var(--terminal-glow);\n        }\n        \n        \n        .slide-title {\n            font-size: clamp(2rem, 4vw, 3.5rem);\n            font-weight: 700;\n            margin: 0;\n            position: relative;\n            text-transform: uppercase;\n        }\n\n        .glitch-effect {\n            position: relative;\n        }\n        .glitch-effect::before,\n        .glitch-effect::after {\n            content: attr(data-text);\n            position: absolute;\n            top: 0; left: 0; width: 100%; height: 100%;\n            background: var(--terminal-bg);\n            overflow: hidden;\n        }\n        .glitch-effect::before {\n            left: 2px;\n            text-shadow: -2px 0 var(--glitch-color-1);\n            animation: glitch-anim-1 3s infinite linear reverse; \n        }\n        .glitch-effect::after {\n            left: -2px;\n            text-shadow: -2px 0 var(--glitch-color-2);\n            animation: glitch-anim-2 2.5s infinite linear reverse; \n        }\n        @keyframes glitch-anim-1 { 0%, 100% { clip-path: inset(45% 0 56% 0); } 25% { clip-path: inset(0 0 100% 0); } 50% { clip-path: inset(80% 0 2% 0); } 75% { clip-path: inset(40% 0 45% 0); } }\n        @keyframes glitch-anim-2 { 0%, 100% { clip-path: inset(65% 0 30% 0); } 25% { clip-path: inset(20% 0 75% 0); } 50% { clip-path: inset(90% 0 1% 0); } 75% { clip-path: inset(10% 0 88% 0); } }\n        \n        .cursor {\n            display: inline-block;\n            width: 1rem;\n            height: 1.8rem;\n            background: var(--terminal-text);\n            animation: blink 1.2s steps(1) infinite;\n            margin-left: 10px;\n            box-shadow: 0 0 5px var(--terminal-glow);\n        }\n        @keyframes blink { 50% { opacity: 0; } }\n        \n        .slide-content {\n            flex: 1;\n            padding: 30px 60px;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            max-height: 580px;\n            overflow: auto;\n            font-size: clamp(1rem, 2.5vw, 1.4rem);\n            line-height: 1.7;\n            text-shadow: 0 0 2px var(--terminal-glow);\n        }\n        \n        .content-points { list-style: none; padding: 0; margin: 0; }\n        .content-points li { margin-bottom: 15px; position: relative; padding-left: 30px; }\n        .content-points li::before {\n            content: \">>\"; \n            position: absolute;\n            left: 0;\n            font-weight: bold;\n            color: var(--terminal-text-darker); \n        }\n        \n        .slide-footer {\n            position: absolute;\n            bottom: 0; left: 0; right: 0;\n            padding: 10px 30px;\n            background: var(--terminal-text-darker);\n            color: var(--terminal-bg);\n            font-size: 16px;\n            font-weight: bold;\n            display: flex;\n            justify-content: flex-end;\n            text-shadow: none;\n        }\n        \n        .system-message-box {\n            border: 1px dotted var(--terminal-text-darker);\n            padding: 20px;\n            margin: 20px 0;\n            background: rgba(61, 220, 132, 0.05);\n        }\n        .system-message-box::before {\n            content: \"[LOG]: \";\n            font-weight: bold;\n            color: var(--terminal-text-darker);\n        }\n\n        @media (max-width: 1280px) {\n            body { width: 100vw; height: 56.25vw; max-height: 100vh; }\n            .slide-container { border: none; border-radius: 0; box-shadow: inset 0 0 40px rgba(0,0,0,0.7); }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"slide-container\">\n        <header class=\"slide-header\">\n            <h1 class=\"slide-title glitch-effect\" data-text=\"{{ main_heading }}\">{{ main_heading }}<span class=\"cursor\"></span></h1>\n        </header>\n        \n        <main class=\"slide-content\">\n            <p>{{ page_content }}</p>\n        </main>\n        \n        <footer class=\"slide-footer\">\n            <span>NODE: {{ current_page_number }} | TOTAL: {{ total_page_count }} | STATUS: STABLE</span>\n        </footer>\n    </div>\n</body>\n</html>", "tags": ["终端", "编程", "游戏"], "is_default": false, "export_info": {"exported_at": "2025-06-28T10:12:44.559Z", "original_id": 2, "original_created_at": 1749556645.1032526}}