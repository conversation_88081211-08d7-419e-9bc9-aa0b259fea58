{"template_name": "液体玻璃", "description": "", "html_template": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ page_title }}</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js\"></script>\n    <style>\n        /* --- 基础与背景设置 --- */\n        html {\n            height: 100%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background-color: #0a0a1a;\n        }\n\n        body {\n            width: 1280px;\n            height: 720px;\n            margin: 0;\n            padding: 0;\n            position: relative;\n            overflow: hidden;\n            font-family: 'Inter', 'Microsoft YaHei', 'PingFang SC', sans-serif;\n            flex-shrink: 0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }\n\n\n        body::before {\n            content: '';\n            position: absolute;\n            top: -20%; left: -20%; width: 140%; height: 140%;\n            background: \n                radial-gradient(circle at 20% 80%, rgba(0, 110, 255, 0.3), transparent 40%),\n                radial-gradient(circle at 75% 30%, rgba(180, 50, 255, 0.3), transparent 40%);\n            z-index: 1;\n            animation: liquid-flow-outer 30s ease-in-out infinite alternate;\n        }\n        \n    \n        body::after {\n            content: '';\n            position: absolute;\n            width: 1280px; /* 与幻灯片尺寸相同 */\n            height: 720px;\n            background: \n                radial-gradient(circle at 20% 80%, rgba(0, 110, 255, 0.4), transparent 40%),\n                radial-gradient(circle at 75% 30%, rgba(180, 50, 255, 0.4), transparent 40%);\n            z-index: 2; /* 在外层背景之上，幻灯片面板之下 */\n            border-radius: 40px; /* 与幻灯片面板圆角相同 */\n            animation: liquid-flow-inner 20s ease-in-out infinite alternate;\n        }\n\n        @keyframes liquid-flow-outer {\n            from { transform: rotate(0deg) scale(1.2); filter: blur(80px); }\n            to   { transform: rotate(180deg) scale(1.5); filter: blur(100px); }\n        }\n        \n        @keyframes liquid-flow-inner {\n            from { transform: rotate(0deg) scale(1); filter: blur(40px); }\n            to   { transform: rotate(-180deg) scale(1.2); filter: blur(50px); }\n        }\n\n        \n        .slide-container {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            flex-direction: column;\n            color: white;\n            position: relative;\n            z-index: 3; \n            \n            \n            background: rgba(10, 10, 30, 0.1);\n            backdrop-filter: blur(50px) saturate(180%);\n            -webkit-backdrop-filter: blur(50px) saturate(180%);\n            \n            \n            border-radius: 40px;\n            border: 1.5px solid transparent;\n            background-clip: padding-box;\n            box-shadow: \n                inset 0 0 2px 1px rgba(255, 255, 255, 0.2), \n                0 10px 40px rgba(0, 0, 0, 0.5); \n        }\n        \n        .slide-header {\n            padding: 40px 60px 20px 60px;\n            border-bottom: 1px solid rgba(255, 255, 255, 0.15);\n        }\n        \n        .slide-title {\n            font-size: clamp(2.2rem, 4vw, 3.8rem);\n            font-weight: 800; \n            color: #ffffff;\n            margin: 0;\n            line-height: 1.2;\n            text-shadow: 0 0 15px rgba(200, 220, 255, 0.5);\n        }\n        \n        .slide-content {\n            flex: 1;\n            padding: 30px 60px;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            max-height: 580px;\n            overflow: auto;\n        }\n        \n        .content-main {\n            font-size: clamp(1.1rem, 2.5vw, 1.5rem);\n            line-height: 1.7; \n            color: #e2e8f0;\n        }\n        \n        .content-points {\n            list-style: none; padding: 0; margin: 20px 0 0 0;\n        }\n        \n        .content-points li {\n            margin-bottom: 20px; padding-left: 40px; position: relative; font-size: 1.25rem;\n        }\n        \n        /* 列表符号: \"水滴/气泡\" 效果 */\n        .content-points li:before {\n            content: \"\";\n            position: absolute;\n            left: 5px; top: 10px;\n            width: 14px; height: 14px;\n            border-radius: 50%;\n            background: radial-gradient(circle at 30% 30%, #fff, #a792ff);\n            box-shadow: \n                inset 0 0 3px rgba(0,0,0,0.3),\n                0 0 10px #a792ff, 0 0 15px #a792ff;\n        }\n        \n        .slide-footer {\n            position: absolute;\n            bottom: 25px;\n            right: 40px;\n            font-size: 16px;\n            color: rgba(255, 255, 255, 0.4);\n            font-weight: 600;\n        }\n        \n        /* --- 特殊组件样式 --- */\n        .highlight-box {\n            background: rgba(0, 110, 255, 0.1);\n            border-left: 4px solid #006eff;\n            padding: 20px 25px;\n            margin: 20px 0;\n            border-radius: 0 16px 16px 0;\n            backdrop-filter: blur(10px);\n            -webkit-backdrop-filter: blur(10px);\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 25px;\n            margin: 20px 0;\n        }\n        \n        /* 小卡片: \"小透镜\" */\n        .stat-card {\n            background: rgba(255, 255, 255, 0.05);\n            padding: 25px 20px;\n            border-radius: 20px; /* 更圆润的边角 */\n            text-align: center;\n            border: 1px solid rgba(255, 255, 255, 0.15);\n            backdrop-filter: blur(15px);\n            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);\n        }\n\n        .stat-card:hover {\n            transform: translateY(-8px) scale(1.03);\n            background: rgba(255, 255, 255, 0.1);\n            box-shadow: 0 8px 30px rgba(0,0,0,0.3);\n            border-color: rgba(255, 255, 255, 0.25);\n        }\n        \n        .stat-number {\n            font-size: 3rem;\n            font-weight: 700;\n            background: linear-gradient(45deg, #e0c3fc, #8ec5fc);\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n            display: block;\n        }\n        \n        .stat-label {\n            font-size: 1rem;\n            color: rgba(255, 255, 255, 0.7);\n            margin-top: 5px;\n        }\n        \n        /* 响应式调整 */\n        @media (max-width: 1280px) {\n            body, body::after {\n                width: 95vw;\n                height: 53.4375vw; /* 保持16:9比例 */\n                max-height: 95vh;\n            }\n        }\n    </style>\n</head>\n<body>\n    <!-- 同样的HTML结构, 仅通过CSS实现全新风格 -->\n    <div class=\"slide-container\">\n        <div class=\"slide-header\">\n            <h1 class=\"slide-title\">{{ main_heading }}</h1>\n        </div>\n        \n        <div class=\"slide-content\">\n            <div class=\"content-main\">\n                {{ page_content }}\n                \n                <!-- 示例: 统计卡片网格 -->\n                <!-- <div class=\"stats-grid\">\n                    <div class=\"stat-card\">\n                        <span class=\"stat-number\">75%</span>\n                        <span class=\"stat-label\">完成率</span>\n                    </div>\n                    <div class=\"stat-card\">\n                        <span class=\"stat-number\">1,204</span>\n                        <span class=\"stat-label\">活跃用户</span>\n                    </div>\n                    <div class=\"stat-card\">\n                        <span class=\"stat-number\">3.1M</span>\n                        <span class=\"stat-label\">总收入</span>\n                    </div>\n                </div> -->\n\n            </div>\n        </div>\n        \n        <div class=\"slide-footer\">\n            {{ current_page_number }} / {{ total_page_count }}\n        </div>\n    </div>\n</body>\n</html>", "tags": ["液体", "玻璃"], "is_default": false, "export_info": {"exported_at": "2025-06-28T10:13:28.839Z", "original_id": 4, "original_created_at": 1750585141.5831485}}