<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}LandPPT - AI PPT Generator{% endblock %}</title>
    <link rel="icon" type="image/svg+xml" href="/static/images/landppt-logo.svg">
    <link rel="alternate icon" href="/static/images/favicon.ico">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @import url('https://fonts.loli.net/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

            --neumorphism-light: #ffffff;
            --neumorphism-dark: #d1d9e6;
            --neumorphism-shadow-light: rgba(255, 255, 255, 0.7);
            --neumorphism-shadow-dark: rgba(163, 177, 198, 0.6);

            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-muted: #718096;

            --border-radius-sm: 12px;
            --border-radius-md: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;

            --spacing-xs: 8px;
            --spacing-sm: 12px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            --spacing-2xl: 48px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--primary-gradient);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-lg);
        }

        .header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            padding: var(--spacing-xl) var(--spacing-lg);
            margin-bottom: var(--spacing-2xl);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--glass-shadow);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-gradient);
            opacity: 0.8;
        }

        .header h1 {
            text-align: center;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: var(--spacing-md);
            letter-spacing: -0.02em;
            position: relative;
        }

        .header h1::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--accent-gradient);
            border-radius: 2px;
        }

        .header p {
            text-align: center;
            color: var(--text-secondary);
            font-size: 1.2rem;
            font-weight: 500;
            opacity: 0.9;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-xl);
            flex-wrap: nowrap;
        }

        .nav-main {
            display: flex;
            gap: var(--spacing-sm);
            flex: 1;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-user {
            display: flex;
            gap: var(--spacing-xs);
            flex-shrink: 0;
        }

        .nav a {
            position: relative;
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--border-radius-md);
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            white-space: nowrap;
        }

        .nav-user a {
            padding: var(--spacing-sm) var(--spacing-sm);
            font-size: 0.85rem;
            background: rgba(255,255,255,0.15);
        }

        .nav a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: -1;
        }

        .nav a:hover {
            color: white;
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
        }

        .nav a:hover::before {
            left: 0;
        }

        .nav a:active {
            transform: translateY(-2px) scale(0.98);
        }
        
        .main-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            padding: var(--spacing-2xl);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--glass-shadow);
            margin-bottom: var(--spacing-2xl);
            position: relative;
            overflow: hidden;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
        }

        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.9);
            padding: var(--spacing-xl);
            font-size: 1rem;
            font-weight: 500;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--glass-border);
        }
        
        .btn {
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-md) var(--spacing-xl);
            background: var(--primary-gradient);
            color: white;
            text-decoration: none;
            border-radius: var(--border-radius-md);
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            font-family: inherit;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin: var(--spacing-xs);
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s;
        }

        .btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:active {
            transform: translateY(-1px) scale(0.98);
        }

        .btn-primary {
            background: var(--primary-gradient);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .btn-success {
            background: var(--success-gradient);
            box-shadow: 0 8px 25px rgba(67, 233, 123, 0.3);
        }
        .btn-warning {
            background: var(--warning-gradient);
            box-shadow: 0 8px 25px rgba(250, 112, 154, 0.3);
        }
        .btn-danger {
            background: var(--secondary-gradient);
            box-shadow: 0 8px 25px rgba(245, 87, 108, 0.3);
        }
        .btn-info {
            background: var(--accent-gradient);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
        }
        
        .form-group {
            margin-bottom: var(--spacing-xl);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1rem;
            letter-spacing: 0.01em;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-md);
            color: var(--text-primary);
            font-size: 1rem;
            font-family: inherit;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.4);
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: var(--text-muted);
            opacity: 0.8;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 6px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background: #d4edda;
            border-color: #27ae60;
            color: #155724;
        }
        
        .alert-error {
            background: #f8d7da;
            border-color: #e74c3c;
            color: #721c24;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-color: #3498db;
            color: #0c5460;
        }
        
        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-lg);
            box-shadow: var(--glass-shadow);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(31, 38, 135, 0.5);
        }

        .card:hover::before {
            transform: scaleX(1);
        }

        .grid {
            display: grid;
            gap: var(--spacing-xl);
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        }

        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: var(--spacing-xl);
            right: var(--spacing-xl);
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            z-index: 1000;
        }

        .fab:hover {
            transform: scale(1.1) rotate(90deg);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
        }

        /* Override fab styles for footer github icon */
        .footer-github-icon {
            position: static !important;
            width: auto !important;
            height: auto !important;
            background: none !important;
            border-radius: 0 !important;
            box-shadow: none !important;
            transform: none !important;
            z-index: auto !important;
            bottom: auto !important;
            right: auto !important;
            display: inline !important;
        }
        
        @media (max-width: 1200px) {
            .nav {
                flex-direction: column;
                gap: var(--spacing-md);
            }

            .nav-main {
                justify-content: center;
                order: 1;
            }

            .nav-user {
                justify-content: center;
                order: 2;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .main-content {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .nav-main {
                flex-direction: column;
                align-items: center;
                gap: var(--spacing-sm);
            }

            .nav-user {
                flex-direction: row;
                gap: var(--spacing-sm);
            }

            .nav a {
                min-width: 120px;
                text-align: center;
                font-size: 0.85rem;
            }

            .nav-user a {
                min-width: 80px;
                font-size: 0.8rem;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>
                <span style="display: inline-flex; align-items: center; gap: 12px;">
                    <img src="/static/images/landppt-logo.svg" alt="LandPPT Logo" style="width: 48px; height: 48px;">
                    LandPPT
                </span>
            </h1>
            <p>AI-powered presentation generation platform</p>
            <nav class="nav">
                <div class="nav-main">
                    <a href="/home"><i class="fas fa-home"></i> 首页</a>
                    <a href="/scenarios"><i class="fas fa-list-alt"></i> 场景选择</a>
                    <a href="/projects"><i class="fas fa-chart-bar"></i> 项目列表</a>
                    <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> 项目仪表板</a>
                    <a href="/global-master-templates"><i class="fas fa-palette"></i> 全局模板管理</a>
                    <a href="/image-gallery"><i class="fas fa-images"></i> 本地图床</a>
                    <a href="/ai-config"><i class="fas fa-robot"></i> 系统配置</a>
                    <!-- <a href="/docs"><i class="fas fa-book"></i> API文档</a> -->
                </div>

                <div class="nav-user">
                    <a href="/auth/profile" title="个人资料">
                        <i class="fas fa-user"></i> 个人资料
                    </a>
                    <a href="/auth/logout" title="退出登录"
                       onclick="return confirm('确定要退出登录吗？')">
                        <i class="fas fa-sign-out-alt"></i> 退出
                    </a>
                </div>
            </nav>
        </header>
        
        <main class="main-content">
            {% block content %}{% endblock %}
        </main>
        
        <footer class="footer">
            <p>&copy; 2025 LandPPT(V0.1.2) - AI PPT Generator. <a href="https://github.com/sligter/LandPPT" target="_blank" style="color: inherit; text-decoration: none;"><i class="fab fa-github footer-github-icon" style="margin-left: 6px;"></i></a></p>
        </footer>
    </div>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
